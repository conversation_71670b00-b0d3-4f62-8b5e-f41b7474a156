# LORE-TSR TableLabelMe数据格式适配需求规划

## 📋 项目概述

### 核心目标
在保持对现有WTW-COCO格式完全兼容的前提下，增量式地集成TableLabelMe格式支持，实现两种数据格式的并行使用。

### 设计原则
- **完全兼容**：对现有COCO格式数据处理流程零侵入
- **增量集成**：通过并行机制支持新格式，不修改现有代码
- **格式转换**：实现TableLabelMe到LORE-TSR内部格式的无损转换
- **多源支持**：支持多个数据目录的统一加载和处理

## 🔄 数据格式对比分析

### 现有格式：WTW-COCO
**目录结构**：
```
data_dir/
├── images/           # 共享图像目录
└── json/
    ├── train.json    # 训练集标注
    ├── test.json     # 测试集标注
    └── val.json      # 验证集标注
```

**标注格式关键字段**：
```json
{
  "logic_axis": [[row_start, row_end, col_start, col_end]],
  "segmentation": [[x1, y1, x2, y2, x3, y3, x4, y4]],
  "bbox": [x, y, width, height],
  "area": 13776.0
}
```

### 目标格式：TableLabelMe
**目录结构**：
```
data_root/
├── part_0001/
│   ├── xxx.jpg/png
│   ├── xxx.json 或 xxx_table_annotation.json
│   └── ...
├── part_0002/
│   ├── xxx.jpg/png
│   ├── xxx.json 或 xxx_table_annotation.json
│   └── ...
└── ...
```

**标注格式关键字段**：
```json
{
  "lloc": {
    "start_row": 0, "end_row": 0,
    "start_col": 0, "end_col": 0
  },
  "bbox": {
    "p1": [x1, y1], "p2": [x2, y2],
    "p3": [x3, y3], "p4": [x4, y4]
  },
  "quality": "合格"
}
```

## 🎯 迭代规划

### 迭代1：MVP版本 - 基础配置系统搭建
**目标**：建立TableLabelMe数据集的基础配置框架，实现最小可用版本

**功能范围**：
- 创建预定义路径配置文件
- 扩展命令行参数系统
- 实现基础的配置加载和验证机制

**验收标准**：
- 能够通过`--dataset tableme`参数启动系统
- 配置文件能够正确加载预定义路径
- 系统能够识别TableLabelMe数据集类型

**开发任务**：
1. 创建`src/lib/configs/dataset_paths.py`配置文件
2. 修改`src/lib/opts.py`添加新参数：
   - `--dataset='tableme'`
   - `--dataset_name='TableLabelMe'`
   - `--data_config='tableme_full'`
3. 实现配置验证逻辑

**文件清单**：
- 新建：`src/lib/configs/dataset_paths.py`
- 修改：`src/lib/opts.py`

### 迭代2：数据集类框架实现
**目标**：创建TableLabelMe数据集类的基础框架，实现数据扫描功能

**功能范围**：
- 创建TableMe数据集类继承自Table基类
- 实现多源数据目录扫描逻辑
- 实现图像和标注文件的配对机制

**验收标准**：
- TableMe类能够正确扫描多个数据根目录
- 能够找到所有有效的图像-标注文件对
- 输出数据扫描统计信息

**开发任务**：
1. 创建`src/lib/datasets/dataset/table_tableme.py`
2. 实现TableMe类基础框架
3. 实现`_scan_multi_source_data()`方法
4. 实现`_scan_part_directory()`方法
5. 实现`_find_annotation_file()`方法

**文件清单**：
- 新建：`src/lib/datasets/dataset/table_tableme.py`

### 迭代3：格式转换核心逻辑
**目标**：实现TableLabelMe到LORE-TSR内部格式的完整转换

**功能范围**：
- 实现逻辑位置转换（lloc -> logic_axis）
- 实现几何信息转换（bbox -> segmentation + bbox + area）
- 实现完整的格式转换函数

**验收标准**：
- 逻辑位置转换精度100%准确
- 几何信息转换保持坐标精度
- 转换后的数据格式完全兼容LORE-TSR内部处理

**开发任务**：
1. 实现`convert_logic_axis()`函数
2. 实现`convert_bbox_format()`函数
3. 实现`calculate_polygon_area()`函数（鞋带公式）
4. 实现`convert_tablelabelme_to_coco()`主转换函数
5. 添加转换精度验证测试

**文件清单**：
- 修改：`src/lib/datasets/dataset/table_tableme.py`

### 迭代4：质量过滤机制
**目标**：实现基于quality字段的数据质量过滤功能

**功能范围**：
- 解析标注文件中的quality字段
- 过滤quality='合格'的样本
- 输出详细的质量统计信息

**验收标准**：
- 只加载quality='合格'的样本
- 输出完整的质量统计报告
- 处理质量字段缺失的异常情况

**开发任务**：
1. 实现`_filter_by_quality()`方法
2. 实现质量统计逻辑
3. 添加异常处理机制
4. 实现统计信息输出

**文件清单**：
- 修改：`src/lib/datasets/dataset/table_tableme.py`

### 迭代5：数据加载流程集成
**目标**：实现完整的数据加载流程，支持训练时的数据获取

**功能范围**：
- 重写`__getitem__`方法
- 集成格式转换到数据加载流程
- 实现与现有训练流程的兼容

**验收标准**：
- 数据加载返回格式与现有COCO格式完全一致
- 支持批量数据加载
- 数据加载性能在可接受范围内

**开发任务**：
1. 重写`__getitem__`方法
2. 集成格式转换逻辑
3. 实现数据预处理流程
4. 添加数据加载性能优化

**文件清单**：
- 修改：`src/lib/datasets/dataset/table_tableme.py`

### 迭代6：系统集成和工厂注册
**目标**：将TableMe数据集集成到LORE-TSR的数据集工厂系统

**功能范围**：
- 在数据集工厂中注册TableMe类
- 实现数据集类型的动态选择
- 确保与现有数据集类型的兼容性

**验收标准**：
- 通过`--dataset tableme`能够正确创建TableMe实例
- 现有数据集类型功能不受影响
- 数据集工厂能够正确路由到对应的数据集类

**开发任务**：
1. 修改`src/lib/datasets/dataset_factory.py`
2. 添加TableMe类的导入和映射
3. 测试数据集工厂的路由功能

**文件清单**：
- 修改：`src/lib/datasets/dataset_factory.py`

### 迭代7：错误处理和容错机制
**目标**：完善系统的错误处理和容错能力

**功能范围**：
- 文件解析错误处理
- 文件缺失异常处理
- 格式错误容错机制
- 路径配置错误处理

**验收标准**：
- 系统能够优雅处理各种异常情况
- 错误信息清晰明确，便于调试
- 异常情况不会导致系统崩溃

**开发任务**：
1. 实现`safe_load_annotation()`函数
2. 添加文件存在性检查
3. 实现JSON解析异常处理
4. 添加详细的错误日志输出

**文件清单**：
- 修改：`src/lib/datasets/dataset/table_tableme.py`

### 迭代8：多任务类型支持
**目标**：支持ctdet、ctdet_mid、ctdet_small三种任务类型

**功能范围**：
- 适配不同分辨率的任务类型
- 实现分辨率相关的数据预处理
- 确保各任务类型的数据格式一致性

**验收标准**：
- 支持1024×1024（ctdet）分辨率
- 支持768×768（ctdet_mid）分辨率  
- 支持512×512（ctdet_small）分辨率
- 各分辨率下数据加载正常

**开发任务**：
1. 实现分辨率适配逻辑
2. 添加任务类型检测
3. 实现分辨率相关的数据预处理
4. 测试各任务类型的兼容性

**文件清单**：
- 修改：`src/lib/datasets/dataset/table_tableme.py`

## 📊 最终验收标准

### 功能验收
1. **配置系统**：能够通过`--data_config`参数选择预定义路径配置
2. **数据加载**：能够正确扫描和加载多源TableLabelMe格式数据
3. **格式转换**：TableLabelMe格式能够无损转换为LORE-TSR内部格式
4. **质量过滤**：只加载`quality='合格'`的样本，并输出统计信息
5. **任务支持**：支持`ctdet`、`ctdet_mid`、`ctdet_small`三种任务类型
6. **兼容性**：现有COCO格式数据处理流程完全不受影响

### 性能验收
- **数据加载时间**：多源数据扫描时间在可接受范围内
- **内存使用**：数据加载不会造成内存泄漏
- **转换精度**：坐标转换精度损失控制在业界标准范围内

### 使用验收
```bash
# 验收测试命令
python main.py ctdet_mid --dataset tableme --exp_id train_wireless --dataset_name TableLabelMe --data_config tableme_full --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5
```

## 📁 完整文件清单

### 新建文件
- `src/lib/configs/dataset_paths.py` - 预定义路径配置

### 修改文件
- `src/lib/datasets/dataset/table_tableme.py` - TableLabelMe数据集类
- `src/lib/opts.py` - 添加新的命令行参数
- `src/lib/datasets/dataset_factory.py` - 添加数据集映射

### 依赖文件（无需修改）
- `src/lib/datasets/dataset/table_mid.py` - 继承基类
- 现有COCO格式相关文件保持不变

## 🚀 开发时间估算

- **迭代1**：1天（配置系统搭建）
- **迭代2**：1天（数据集类框架）
- **迭代3**：2天（格式转换核心逻辑）
- **迭代4**：1天（质量过滤机制）
- **迭代5**：1天（数据加载流程集成）
- **迭代6**：0.5天（系统集成）
- **迭代7**：0.5天（错误处理）
- **迭代8**：1天（多任务类型支持）

**总计**：8个工作日

每个迭代都是独立可验证的模块，可以单独开发和测试，确保开发过程的可控性和质量。
