# LORE-TSR TableLabelMe数据格式适配需求规划

## 📋 项目概述

### 核心目标
在保持对现有WTW-COCO格式完全兼容的前提下，增量式地集成TableLabelMe格式支持，实现两种数据格式的并行使用。

### 设计原则
- **完全兼容**：对现有COCO格式数据处理流程零侵入
- **增量集成**：通过并行机制支持新格式，不修改现有代码
- **格式转换**：实现TableLabelMe到LORE-TSR内部格式的无损转换
- **多源支持**：支持多个数据目录的统一加载和处理

## 🔄 数据格式对比

### 现有格式：WTW-COCO
**目录结构**：
```
data_dir/
├── images/           # 共享图像目录
└── json/
    ├── train.json    # 训练集标注
    ├── test.json     # 测试集标注
    └── val.json      # 验证集标注
```

**标注格式关键字段**：
```json
{
  "logic_axis": [[row_start, row_end, col_start, col_end]],
  "segmentation": [[x1, y1, x2, y2, x3, y3, x4, y4]],
  "bbox": [x, y, width, height],
  "area": 13776.0
}
```

### 目标格式：TableLabelMe
**目录结构**：
```
data_root/
├── part_0001/
│   ├── xxx.jpg/png
│   ├── xxx.json 或 xxx_table_annotation.json
│   └── ...
├── part_0002/
│   ├── xxx.jpg/png
│   ├── xxx.json 或 xxx_table_annotation.json
│   └── ...
└── ...
```

**标注格式关键字段**：
```json
{
  "lloc": {
    "start_row": 0, "end_row": 0,
    "start_col": 0, "end_col": 0
  },
  "bbox": {
    "p1": [x1, y1], "p2": [x2, y2],
    "p3": [x3, y3], "p4": [x4, y4]
  },
  "quality": "合格"
}
```

## 🎯 迭代规划

### 迭代1：MVP - 基础配置系统（优先级：P0）

**目标**：建立最小可用的配置系统，支持单一数据源的TableLabelMe格式加载

**功能范围**：
- 创建预定义路径配置文件
- 扩展命令行参数系统
- 实现基础的数据路径配置加载

**验收标准**：
- 能够通过`--dataset tableme --data_config tableme_subset`参数启动程序
- 配置系统能够正确加载预定义路径
- 不影响现有COCO格式的使用

**涉及文件**：
- 新建：`src/lib/configs/dataset_paths.py`
- 修改：`src/lib/opts.py`

**预定义配置内容**：
```python
DATASET_PATH_CONFIGS = {
    'tableme_subset': {
        'train': ['/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/train'],
        'val': ['/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/val']
    }
}
```

### 迭代2：核心格式转换模块（优先级：P0）

**目标**：实现TableLabelMe到COCO格式的核心转换逻辑

**功能范围**：
- 实现逻辑位置转换函数（lloc -> logic_axis）
- 实现几何信息转换函数（bbox -> segmentation + bbox + area）
- 实现主转换函数
- 添加转换精度验证

**验收标准**：
- 转换函数能够正确处理TableLabelMe格式的所有必要字段
- 转换结果与COCO格式完全兼容
- 坐标转换精度损失在可接受范围内
- 包含完整的单元测试

**核心转换逻辑**：
- `convert_logic_axis()`: lloc字典 -> logic_axis列表
- `convert_bbox_format()`: 四点bbox -> segmentation + bbox + area
- `convert_tablelabelme_to_coco()`: 完整转换函数

### 迭代3：基础数据集类实现（优先级：P0）

**目标**：创建TableMe数据集类，实现单源数据加载

**功能范围**：
- 创建继承自Table的TableMe类
- 实现单个数据根目录的扫描逻辑
- 实现图像-标注文件配对逻辑
- 集成格式转换模块

**验收标准**：
- 能够正确扫描part_xxxx子目录结构
- 能够匹配图像文件和对应的标注文件（.json或_table_annotation.json）
- 数据加载过程中能够正确应用格式转换
- 支持基础的错误处理和日志输出

**涉及文件**：
- 新建：`src/lib/datasets/dataset/table_tableme.py`

### 迭代4：数据集工厂集成（优先级：P0）

**目标**：将TableMe数据集类集成到现有的数据集工厂系统

**功能范围**：
- 在dataset_factory中添加tableme映射
- 确保正确的模块导入
- 端到端功能测试

**验收标准**：
- 能够通过`--dataset tableme`参数成功创建TableMe数据集实例
- 数据集能够正常返回训练样本
- 现有COCO格式数据集功能完全不受影响

**涉及文件**：
- 修改：`src/lib/datasets/dataset_factory.py`

### 迭代5：质量过滤机制（优先级：P1）

**目标**：实现基于quality字段的样本过滤功能

**功能范围**：
- 实现quality字段检查逻辑
- 只加载quality='合格'的样本
- 输出详细的质量统计信息
- 添加质量过滤的配置选项

**验收标准**：
- 能够正确识别和过滤quality字段
- 输出包含合格/不合格/缺失等统计信息
- 过滤后的数据集只包含合格样本
- 支持通过配置禁用质量过滤（用于调试）

### 迭代6：多源数据支持（优先级：P1）

**目标**：扩展支持多个数据根目录的统一加载

**功能范围**：
- 扩展预定义配置支持多路径
- 实现多源数据目录的并行扫描
- 统一样本索引和管理
- 添加数据源统计信息

**验收标准**：
- 支持tableme_full配置（包含4个数据源）
- 能够正确合并多个数据源的样本
- 输出每个数据源的样本统计
- 保持数据加载性能在可接受范围

**扩展配置**：
```python
'tableme_full': {
    'train': [
        '/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_chinese/train',
        '/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_english/train',
        '/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/train',
        '/aipdf-mlp/shared/tsr_training/wired_table/release/TALOCRTable/train'
    ],
    'val': [对应的val路径]
}
```

### 迭代7：多任务类型支持（优先级：P2）

**目标**：确保TableMe数据集支持所有现有任务类型

**功能范围**：
- 验证ctdet任务支持（1024×1024分辨率）
- 验证ctdet_mid任务支持（768×768分辨率）
- 验证ctdet_small任务支持（512×512分辨率）
- 添加任务特定的数据预处理逻辑

**验收标准**：
- 三种任务类型都能正常使用TableMe数据集
- 不同分辨率下的数据预处理正确
- 训练脚本能够正常启动和运行

### 迭代8：错误处理和容错机制（优先级：P2）

**目标**：完善错误处理，提高系统稳定性

**功能范围**：
- 实现文件解析异常处理
- 添加文件缺失检查和处理
- 实现格式错误验证和报告
- 添加路径配置错误处理

**验收标准**：
- 遇到损坏的标注文件时能够跳过并继续
- 文件缺失时输出明确的错误信息
- 配置错误时抛出有意义的异常
- 系统在部分数据损坏时仍能正常运行

### 迭代9：性能优化和监控（优先级：P3）

**目标**：优化数据加载性能，添加性能监控

**功能范围**：
- 优化多源数据扫描性能
- 添加数据加载时间监控
- 实现内存使用监控
- 添加数据加载进度显示

**验收标准**：
- 多源数据扫描时间在合理范围内
- 无内存泄漏问题
- 提供清晰的加载进度反馈
- 包含性能基准测试

## 📊 最终验收标准

### 功能完整性
- 支持tableme_subset和tableme_full两种预定义配置
- 支持ctdet、ctdet_mid、ctdet_small三种任务类型
- 质量过滤功能正常工作
- 现有COCO格式完全兼容

### 使用验收命令
```bash
# MVP测试
python main.py ctdet --dataset tableme --dataset_name TableLabelMe --data_config tableme_subset

# 完整功能测试
python main.py ctdet_mid --dataset tableme --exp_id train_wireless --dataset_name TableLabelMe --data_config tableme_full --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5
```

### 预期输出
- 数据集质量统计信息
- 成功加载的样本数量
- 正常的训练流程启动
- 模型训练过程正常进行

## 📁 文件变更清单

### 新建文件
- `src/lib/configs/dataset_paths.py` - 预定义路径配置
- `src/lib/datasets/dataset/table_tableme.py` - TableLabelMe数据集类

### 修改文件
- `src/lib/opts.py` - 添加新的命令行参数
- `src/lib/datasets/dataset_factory.py` - 添加数据集映射

### 依赖文件（无需修改）
- `src/lib/datasets/dataset/table_mid.py` - 继承基类
- 现有COCO格式相关文件保持不变

## ⏱️ 开发时间估算

- 迭代1-4（MVP核心功能）：4个工作日
- 迭代5-6（质量过滤和多源支持）：2个工作日
- 迭代7-9（完善功能）：2个工作日

**总计**：8个工作日
